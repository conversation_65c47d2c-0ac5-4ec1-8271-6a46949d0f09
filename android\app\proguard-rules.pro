# تحسينات ProGuard لتطبيق Flutter

# الاحتفاظ بكلاسات Flutter الأساسية
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# الاحتفاظ بكلاسات Firebase
-keep class com.google.firebase.** { *; }
-keep class com.google.android.gms.** { *; }

# الاحتفاظ بكلاسات PDF Viewer
-keep class com.syncfusion.flutter.pdfviewer.** { *; }

# تحسينات عامة
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontpreverify
-verbose

# إزالة logs في الإصدار النهائي
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# تحسين الكود
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*
