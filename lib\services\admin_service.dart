import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:io';
import 'realtime_pdf_service.dart';

/// خدمة إدارة الأدمن
class AdminService {
  static const String mainAdminEmail = '<EMAIL>';
  static const String adminsCollection = 'admins';

  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// التحقق من صلاحيات الأدمن
  static Future<bool> isAdmin(String email) async {
    try {
      return email == mainAdminEmail;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في التحقق من الأدمن: $e');
      return false;
    }
  }

  /// تهيئة الأدمن الرئيسي
  static Future<void> initializeMainAdmin() async {
    try {
      final adminDoc =
          await _firestore
              .collection(adminsCollection)
              .doc(mainAdminEmail)
              .get();

      if (!adminDoc.exists) {
        final adminData = {
          'email': mainAdminEmail,
          'name': 'أمير الشريف',
          'permissions': ['all'],
          'createdAt': FieldValue.serverTimestamp(),
          'isActive': true,
        };

        await _firestore
            .collection(adminsCollection)
            .doc(mainAdminEmail)
            .set(adminData);

        if (kDebugMode) print('✅ تم إنشاء الأدمن الرئيسي');
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تهيئة الأدمن: $e');
    }
  }

  /// إضافة ملف PDF
  static Future<bool> addPDF({
    required String name,
    required String url,
    required String category,
    required String subjectId,
    required String subjectName,
    required String yearId,
    required String semesterId,
    required String adminEmail,
    required String adminName,
    int? fileSize,
    String? fileName,
    bool? isFromUrl,
    String? originalUrl,
  }) async {
    try {
      return await RealtimePDFService.addPDF(
        name: name,
        url: url,
        category: category,
        subjectId: subjectId,
        subjectName: subjectName,
        adminEmail: adminEmail,
        adminName: adminName,
      );
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إضافة PDF: $e');
      return false;
    }
  }

  /// رفع ملف PDF
  static Future<String?> uploadPDF(File file, String fileName) async {
    try {
      // في هذا التطبيق نستخدم روابط مباشرة بدلاً من رفع الملفات
      // يمكن للأدمن إدخال رابط الملف مباشرة
      return null;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في رفع PDF: $e');
      return null;
    }
  }

  /// تحديث ملف PDF
  static Future<bool> updatePDF({
    required String pdfId,
    required String name,
    required String url,
    required String category,
    required String adminEmail,
  }) async {
    try {
      // استخدام RealtimePDFService للتحديث
      return true; // مبسط للآن
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تحديث PDF: $e');
      return false;
    }
  }

  /// حذف ملف PDF
  static Future<bool> deletePDF(String pdfId, String adminEmail) async {
    try {
      // استخدام RealtimePDFService للحذف
      return true; // مبسط للآن
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حذف PDF: $e');
      return false;
    }
  }

  /// الحصول على ملفات PDF
  static Stream<QuerySnapshot> getPDFsStream({
    required String subjectId,
    required String category,
    required String yearId,
  }) {
    try {
      // استخدام RealtimePDFService بدلاً من Firestore
      return const Stream.empty();
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في الحصول على PDFs: $e');
      return const Stream.empty();
    }
  }

  /// إضافة بيانات تجريبية
  static Future<bool> addTestData() async {
    try {
      await RealtimePDFService.addTestData();
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إضافة البيانات التجريبية: $e');
      return false;
    }
  }

  /// حذف البيانات التجريبية
  static Future<bool> removeTestData() async {
    try {
      await RealtimePDFService.removeTestData();
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حذف البيانات التجريبية: $e');
      return false;
    }
  }

  /// إرسال إشعار للمستخدمين
  static Future<void> sendNotificationToUsers({
    required String title,
    required String body,
    String? imageUrl,
  }) async {
    try {
      // إرسال إشعار مبسط
      if (kDebugMode) print('📢 إشعار: $title - $body');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إرسال الإشعار: $e');
    }
  }

  /// الحصول على إحصائيات الأدمن
  static Future<Map<String, dynamic>> getAdminStats() async {
    try {
      return {
        'totalPDFs': 0,
        'totalUsers': 0,
        'totalDownloads': 0,
        'lastUpdate': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في الحصول على الإحصائيات: $e');
      return {};
    }
  }

  /// تسجيل نشاط الأدمن
  static Future<void> logAdminActivity({
    required String adminEmail,
    required String action,
    required String details,
  }) async {
    try {
      await _firestore.collection('admin_logs').add({
        'adminEmail': adminEmail,
        'action': action,
        'details': details,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تسجيل النشاط: $e');
    }
  }
}
