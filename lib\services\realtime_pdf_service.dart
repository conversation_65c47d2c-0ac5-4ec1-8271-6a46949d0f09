import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';

/// خدمة إدارة ملفات PDF باستخدام Realtime Database
class RealtimePDFService {
  static final FirebaseDatabase _database = FirebaseDatabase.instanceFor(
    app: Firebase.app(),
    databaseURL:
        'https://rabbani-remembrance-default-rtdb.europe-west1.firebasedatabase.app',
  );
  static const String pdfsPath = 'pdfs';

  /// إضافة ملف PDF جديد
  static Future<bool> addPDF({
    required String name,
    required String url,
    required String category,
    required String subjectId,
    required String subjectName,
    String? adminEmail,
    String? adminName,
  }) async {
    try {
      final pdfId = DateTime.now().millisecondsSinceEpoch.toString();

      final pdfData = {
        'id': pdfId,
        'name': name,
        'url': url,
        'subjectName': subjectName,
        'createdAt': DateTime.now().toIso8601String(),
        'uploadedBy': adminEmail ?? 'admin',
        'uploaderName': adminName ?? 'أدمن النظام',
        'isActive': true,
      };

      await _database.ref('$pdfsPath/$subjectId/$category/$pdfId').set(pdfData);

      if (kDebugMode) {
        print('✅ تم إضافة PDF في Realtime Database: $name');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إضافة PDF: $e');
      }
      return false;
    }
  }

  /// الحصول على ملفات PDF لمادة وقسم معين
  static Stream<List<Map<String, dynamic>>> getPDFsStream({
    required String subjectId,
    required String category,
  }) {
    return _database
        .ref('$pdfsPath/$subjectId/$category')
        .onValue
        .distinct()
        .map((event) {
          final data = event.snapshot.value;
          if (data == null) return <Map<String, dynamic>>[];

          final Map<dynamic, dynamic> pdfsMap = data as Map<dynamic, dynamic>;
          final List<Map<String, dynamic>> pdfsList = [];

          pdfsMap.forEach((key, value) {
            if (value is Map && value['isActive'] == true) {
              final pdfData = Map<String, dynamic>.from(value);
              pdfData['id'] = key;
              pdfsList.add(pdfData);
            }
          });

          // ترتيب حسب تاريخ الإنشاء
          pdfsList.sort((a, b) {
            final aDate =
                DateTime.tryParse(a['createdAt'] ?? '') ?? DateTime.now();
            final bDate =
                DateTime.tryParse(b['createdAt'] ?? '') ?? DateTime.now();
            return bDate.compareTo(aDate);
          });

          return pdfsList;
        });
  }

  /// حذف ملف PDF
  static Future<bool> deletePDF({
    required String subjectId,
    required String category,
    required String pdfId,
  }) async {
    try {
      await _database.ref('$pdfsPath/$subjectId/$category/$pdfId').remove();

      if (kDebugMode) {
        print('✅ تم حذف PDF من Realtime Database: $pdfId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في حذف PDF: $e');
      }
      return false;
    }
  }

  /// تحديث ملف PDF
  static Future<bool> updatePDF({
    required String subjectId,
    required String category,
    required String pdfId,
    required Map<String, dynamic> updates,
  }) async {
    try {
      updates['updatedAt'] = DateTime.now().toIso8601String();

      await _database
          .ref('$pdfsPath/$subjectId/$category/$pdfId')
          .update(updates);

      if (kDebugMode) {
        print('✅ تم تحديث PDF في Realtime Database: $pdfId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تحديث PDF: $e');
      }
      return false;
    }
  }

  /// إضافة بيانات تجريبية
  static Future<void> addTestData() async {
    try {
      if (kDebugMode) print('🔄 بدء إضافة بيانات تجريبية...');

      final testData = {
        'fiqh_issues': {
          'أسئلة': {
            'test1': {
              'id': 'test1',
              'name': 'أسئلة قضايا فقهية معاصرة',
              'url':
                  'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
              'subjectName': 'قضايا فقهية معاصرة',
              'createdAt': DateTime.now().toIso8601String(),
              'uploadedBy': 'admin',
              'uploaderName': 'أدمن النظام',
              'isActive': true,
            },
          },
          'ملخصات': {
            'test2': {
              'id': 'test2',
              'name': 'ملخص قضايا فقهية معاصرة',
              'url':
                  'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
              'subjectName': 'قضايا فقهية معاصرة',
              'createdAt': DateTime.now().toIso8601String(),
              'uploadedBy': 'admin',
              'uploaderName': 'أدمن النظام',
              'isActive': true,
            },
          },
        },
        'quran': {
          'أشهر المواضع': {
            'test3': {
              'id': 'test3',
              'name': 'أشهر المواضع في القرآن الكريم',
              'url':
                  'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
              'subjectName': 'القرآن الكريم',
              'createdAt': DateTime.now().toIso8601String(),
              'uploadedBy': 'admin',
              'uploaderName': 'أدمن النظام',
              'isActive': true,
            },
          },
          'الامتحانات': {
            'test4': {
              'id': 'test4',
              'name': 'امتحانات القرآن الكريم',
              'url':
                  'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
              'subjectName': 'القرآن الكريم',
              'createdAt': DateTime.now().toIso8601String(),
              'uploadedBy': 'admin',
              'uploaderName': 'أدمن النظام',
              'isActive': true,
            },
          },
        },
      };

      await _database.ref(pdfsPath).set(testData);

      if (kDebugMode) print('🎉 تم إضافة البيانات التجريبية بنجاح!');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إضافة البيانات التجريبية: $e');
    }
  }

  /// حذف جميع البيانات التجريبية
  static Future<void> removeTestData() async {
    try {
      await _database.ref(pdfsPath).remove();
      if (kDebugMode) print('🗑️ تم حذف جميع البيانات التجريبية');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حذف البيانات التجريبية: $e');
    }
  }
}
