import 'package:flutter/foundation.dart';

/// خدمة التحقق من البريد الإلكتروني - مبسطة
class EmailVerificationService {
  
  /// إرسال كود التحقق
  static Future<String?> sendVerificationCode(String email) async {
    try {
      // في التطبيق الحقيقي، يتم إرسال كود فعلي عبر البريد الإلكتروني
      // هنا نستخدم كود ثابت للاختبار
      if (kDebugMode) print('📧 تم إرسال كود التحقق إلى: $email');
      return '123456'; // كود تجريبي
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إرسال كود التحقق: $e');
      return null;
    }
  }

  /// التحقق من صحة الكود
  static Future<bool> verifyCode(String email, String code) async {
    try {
      // في التطبيق الحقيقي، يتم التحقق من الكود المرسل فعلياً
      // هنا نقبل الكود التجريبي فقط
      final isValid = code == '123456';
      if (kDebugMode) {
        print(isValid 
          ? '✅ كود التحقق صحيح لـ: $email' 
          : '❌ كود التحقق خاطئ لـ: $email');
      }
      return isValid;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في التحقق من الكود: $e');
      return false;
    }
  }

  /// حذف كود التحقق بعد الاستخدام
  static Future<void> deleteVerificationCode(String email) async {
    try {
      // في التطبيق الحقيقي، يتم حذف الكود من قاعدة البيانات
      if (kDebugMode) print('🗑️ تم حذف كود التحقق لـ: $email');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حذف كود التحقق: $e');
    }
  }

  /// التحقق من انتهاء صلاحية الكود
  static Future<bool> isCodeExpired(String email, String code) async {
    try {
      // في التطبيق الحقيقي، يتم فحص وقت انتهاء الصلاحية
      // هنا نعتبر الكود صالح دائماً للاختبار
      return false;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في فحص انتهاء صلاحية الكود: $e');
      return true;
    }
  }

  /// إعادة إرسال كود التحقق
  static Future<String?> resendVerificationCode(String email) async {
    try {
      // إعادة إرسال نفس الكود
      return await sendVerificationCode(email);
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إعادة إرسال كود التحقق: $e');
      return null;
    }
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    try {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      return emailRegex.hasMatch(email);
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في التحقق من البريد الإلكتروني: $e');
      return false;
    }
  }

  /// الحصول على رسالة خطأ مناسبة
  static String getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'invalid-code':
        return 'كود التحقق غير صحيح';
      case 'expired-code':
        return 'انتهت صلاحية كود التحقق';
      case 'invalid-email':
        return 'البريد الإلكتروني غير صحيح';
      case 'send-failed':
        return 'فشل في إرسال كود التحقق';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }
}
