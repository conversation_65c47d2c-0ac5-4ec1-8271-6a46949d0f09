import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// خدمة إضافة بيانات تجريبية للاختبار
class TestDataService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// إضافة ملفات PDF تجريبية للاختبار - مبسطة
  static Future<void> addTestPDFs() async {
    try {
      if (kDebugMode) print('🔄 بدء إضافة ملفات PDF تجريبية...');

      // ملفات تجريبية مبسطة مع روابط حقيقية
      final testPDFs = [
        {
          'id': 'test_fiqh_questions_1',
          'name': 'أسئلة قضايا فقهية معاصرة',
          'url':
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          'category': 'أسئلة',
          'subjectId': 'fiqh_issues',
          'subjectName': 'قضايا فقهية معاصرة',
          'yearId': 'year1',
          'semesterId': 'year1_sem1',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'uploadedBy': '<EMAIL>',
          'uploaderName': 'أدمن النظام',
          'fileSize': 2.5,
          'fileName': 'fiqh_questions.pdf',
          'fileExtension': 'pdf',
          'isFromUrl': true,
          'isActive': true,
          'originalUrl':
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        },

        {
          'id': 'test_fiqh_summary_1',
          'name': 'ملخص قضايا فقهية معاصرة',
          'url':
              'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
          'category': 'ملخصات',
          'subjectId': 'fiqh_issues',
          'subjectName': 'قضايا فقهية معاصرة',
          'yearId': 'year1',
          'semesterId': 'year1_sem1',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'uploadedBy': '<EMAIL>',
          'uploaderName': 'أدمن النظام',
          'fileSize': 1.8,
          'fileName': 'fiqh_summary.pdf',
          'fileExtension': 'pdf',
          'isFromUrl': true,
          'isActive': true,
          'originalUrl':
              'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
        },

        {
          'id': 'test_quran_places_1',
          'name': 'أشهر المواضع في القرآن الكريم',
          'url':
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          'category': 'أشهر المواضع',
          'subjectId': 'quran',
          'subjectName': 'القرآن الكريم',
          'yearId': 'year1',
          'semesterId': 'year1_sem2',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'uploadedBy': '<EMAIL>',
          'uploaderName': 'أدمن النظام',
          'fileSize': 3.2,
          'fileName': 'quran_places.pdf',
          'fileExtension': 'pdf',
          'isFromUrl': true,
          'isActive': true,
          'originalUrl':
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        },

        {
          'id': 'test_quran_exams_1',
          'name': 'امتحانات القرآن الكريم',
          'url':
              'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
          'category': 'الامتحانات',
          'subjectId': 'quran',
          'subjectName': 'القرآن الكريم',
          'yearId': 'year1',
          'semesterId': 'year1_sem2',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'uploadedBy': '<EMAIL>',
          'uploaderName': 'أدمن النظام',
          'fileSize': 2.1,
          'fileName': 'quran_exams.pdf',
          'fileExtension': 'pdf',
          'isFromUrl': true,
          'isActive': true,
          'originalUrl':
              'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
        },

        {
          'id': 'test_civil_law_book_1',
          'name': 'الكتاب الرسمي للقانون المدني',
          'url':
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
          'category': 'الكتاب الرسمي',
          'subjectId': 'civil_law',
          'subjectName': 'القانون المدني',
          'yearId': 'year2',
          'semesterId': 'year2_sem2',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
          'uploadedBy': '<EMAIL>',
          'uploaderName': 'أدمن النظام',
          'fileSize': 5.7,
          'fileName': 'civil_law_book.pdf',
          'fileExtension': 'pdf',
          'isFromUrl': true,
          'isActive': true,
          'originalUrl':
              'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
        },
      ];

      // إضافة الملفات إلى Firestore مباشرة
      for (final pdfData in testPDFs) {
        try {
          await _firestore
              .collection('pdfs')
              .doc(pdfData['id'] as String)
              .set(pdfData);
          if (kDebugMode) print('✅ تم إضافة: ${pdfData['name']}');
        } catch (e) {
          if (kDebugMode) print('❌ خطأ في إضافة ${pdfData['name']}: $e');
        }
      }

      if (kDebugMode) print('🎉 تم إضافة جميع ملفات PDF التجريبية بنجاح!');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ عام في إضافة ملفات PDF التجريبية: $e');
    }
  }

  /// حذف جميع ملفات PDF التجريبية
  static Future<void> removeTestPDFs() async {
    if (!kDebugMode) return;

    try {
      print('🔄 بدء حذف ملفات PDF التجريبية...');

      final testIds = [
        'test_fiqh_issues_questions',
        'test_fiqh_issues_summary',
        'test_quran_famous_places',
        'test_quran_exams',
        'test_civil_law_book',
      ];

      for (final id in testIds) {
        await _firestore.collection('pdfs').doc(id).delete();
        print('🗑️ تم حذف: $id');
      }

      print('🎉 تم حذف جميع ملفات PDF التجريبية!');
    } catch (e) {
      print('❌ خطأ في حذف ملفات PDF التجريبية: $e');
    }
  }
}
