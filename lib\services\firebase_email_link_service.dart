import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// خدمة روابط البريد الإلكتروني عبر Firebase - مبسطة
class FirebaseEmailLinkService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// إرسال رابط التحقق للتسجيل
  static Future<String?> sendVerificationLink(
    String email,
    String password,
    String displayName,
  ) async {
    try {
      // في التطبيق الحقيقي، يتم إنشاء رابط تحقق مخصص
      // هنا نستخدم Firebase Auth العادي
      
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await credential.user!.updateDisplayName(displayName);
        await credential.user!.sendEmailVerification();
        
        if (kDebugMode) print('📧 تم إرسال رابط التحقق إلى: $email');
        return 'verification-link-sent';
      }
      
      return null;
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) print('❌ خطأ Firebase في إرسال رابط التحقق: ${e.message}');
      return null;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إرسال رابط التحقق: $e');
      return null;
    }
  }

  /// إرسال رابط إعادة تعيين كلمة المرور
  static Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      if (kDebugMode) print('📧 تم إرسال رابط إعادة تعيين كلمة المرور إلى: $email');
      return true;
    } on FirebaseAuthException catch (e) {
      if (kDebugMode) print('❌ خطأ Firebase في إرسال رابط إعادة التعيين: ${e.message}');
      return false;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إرسال رابط إعادة التعيين: $e');
      return false;
    }
  }

  /// التحقق من رابط البريد الإلكتروني
  static Future<bool> verifyEmailLink(String link) async {
    try {
      // في التطبيق الحقيقي، يتم التحقق من صحة الرابط
      // هنا نعتبر الرابط صحيح إذا كان يحتوي على نص معين
      final isValid = link.contains('verification') || link.contains('reset');
      
      if (kDebugMode) {
        print(isValid 
          ? '✅ رابط البريد الإلكتروني صحيح' 
          : '❌ رابط البريد الإلكتروني غير صحيح');
      }
      
      return isValid;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في التحقق من رابط البريد الإلكتروني: $e');
      return false;
    }
  }

  /// معالجة رابط البريد الإلكتروني الوارد
  static Future<Map<String, dynamic>?> handleEmailLink(String link) async {
    try {
      if (await verifyEmailLink(link)) {
        // في التطبيق الحقيقي، يتم استخراج البيانات من الرابط
        return {
          'type': link.contains('verification') ? 'verification' : 'reset',
          'email': '<EMAIL>', // يتم استخراجه من الرابط
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };
      }
      return null;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في معالجة رابط البريد الإلكتروني: $e');
      return null;
    }
  }

  /// إنشاء رابط مخصص للتحقق
  static Future<String?> createCustomVerificationLink(
    String email,
    Map<String, dynamic> additionalData,
  ) async {
    try {
      // في التطبيق الحقيقي، يتم إنشاء رابط مخصص
      // هنا نعيد رابط تجريبي
      final baseUrl = 'https://yourapp.com/verify';
      final encodedEmail = Uri.encodeComponent(email);
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      
      final customLink = '$baseUrl?email=$encodedEmail&timestamp=$timestamp';
      
      if (kDebugMode) print('🔗 تم إنشاء رابط مخصص: $customLink');
      return customLink;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إنشاء رابط مخصص: $e');
      return null;
    }
  }

  /// التحقق من انتهاء صلاحية الرابط
  static bool isLinkExpired(String link, {int expiryHours = 24}) {
    try {
      // في التطبيق الحقيقي، يتم استخراج الوقت من الرابط
      // هنا نعتبر الرابط صالح دائماً للاختبار
      return false;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في فحص انتهاء صلاحية الرابط: $e');
      return true;
    }
  }

  /// الحصول على رسالة خطأ مناسبة
  static String getErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'invalid-link':
        return 'الرابط غير صحيح';
      case 'expired-link':
        return 'انتهت صلاحية الرابط';
      case 'already-used':
        return 'تم استخدام الرابط من قبل';
      case 'send-failed':
        return 'فشل في إرسال الرابط';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }
}
